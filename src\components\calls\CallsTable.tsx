
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Eye, Search, X, Filter } from "lucide-react";
import { CallData } from '@/services/mockData';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { formatDateTime } from '@/lib/utils';

interface CallsTableProps {
  calls: CallData[];
  totalCalls?: number;
  onPageChange?: (skip: number, limit: number) => void;
  pagination?: {
    currentPage?: number;
    totalPages?: number;
    hasNext?: boolean;
    hasPrevious?: boolean;
    nextPage?: number | null;
    previousPage?: number | null;
  };
  itemsPerPage?: number;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  // Search functionality
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  // Filter functionality
  sentimentFilter?: string;
  flagsFilter?: string;
  onSentimentFilterChange?: (sentiment: string) => void;
  onFlagsFilterChange?: (flags: string) => void;
}

const CallsTable: React.FC<CallsTableProps> = ({
  calls,
  totalCalls = 0,
  onPageChange,
  pagination,
  itemsPerPage = 5,
  onItemsPerPageChange,
  searchQuery = '',
  onSearchChange,
  sentimentFilter = '',
  flagsFilter = '',
  onSentimentFilterChange,
  onFlagsFilterChange
}) => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);

  // Use API-based pagination if onPageChange is provided, otherwise use client-side pagination
  const isApiPagination = !!onPageChange;

  // Use pagination info from props if available
  const apiCurrentPage = pagination?.currentPage || currentPage;
  const apiTotalPages = pagination?.totalPages || Math.ceil(totalCalls / itemsPerPage);

  // For client-side pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentCalls = isApiPagination ? calls : calls.slice(indexOfFirstItem, indexOfLastItem);
  const totalItems = isApiPagination ? totalCalls : calls.length;
  const totalPages = isApiPagination && pagination ? apiTotalPages : Math.ceil(totalItems / itemsPerPage);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    console.log(`Changing page from ${currentPage} to ${newPage}`);
    setCurrentPage(newPage);

    if (isApiPagination && onPageChange) {
      const skip = (newPage - 1) * itemsPerPage;
      console.log(`Calling onPageChange with skip=${skip}, limit=${itemsPerPage}`);
      onPageChange(skip, itemsPerPage);
    }
  };

  // Use the API's pagination information if available
  useEffect(() => {
    if (pagination) {
      console.log('Pagination information updated:', pagination);
      if (pagination.currentPage) {
        console.log(`Setting current page to ${pagination.currentPage} from pagination info`);
        setCurrentPage(pagination.currentPage);
      }
    }
  }, [pagination]);

  // Reset to page 1 when calls change due to filtering, but not due to pagination
  useEffect(() => {
    // Only reset if we're not in the middle of a pagination operation
    if (!isApiPagination) {
      console.log('Calls length changed due to filtering, resetting to page 1');
      setCurrentPage(1);
    }
  }, [calls.length, isApiPagination]);

  const viewCallDetails = (id: string) => {
    navigate(`/call/${id}`);
  };

  // Render flags as beautiful chips with colors
  const renderFlags = (flags: string[]) => {
    const flagStyles = {
      frustration: { bg: 'bg-red-100', text: 'text-red-800', dot: 'bg-red-500' },
      confusion: { bg: 'bg-yellow-100', text: 'text-yellow-800', dot: 'bg-yellow-500' },
      urgency: { bg: 'bg-orange-100', text: 'text-orange-800', dot: 'bg-orange-500' },
      satisfaction: { bg: 'bg-green-100', text: 'text-green-800', dot: 'bg-green-500' },
      abusive: { bg: 'bg-red-200', text: 'text-red-900', dot: 'bg-red-600' }
    };

    return (
      <div className="flex flex-wrap gap-1">
        {flags.map((flag, index) => {
          const style = flagStyles[flag.toLowerCase() as keyof typeof flagStyles] ||
                       { bg: 'bg-gray-100', text: 'text-gray-800', dot: 'bg-gray-500' };

          return (
            <span
              key={index}
              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${style.bg} ${style.text} shadow-sm`}
              title={`${flag.charAt(0).toUpperCase() + flag.slice(1)} flag detected in this call`}
            >
              <div className={`w-2 h-2 ${style.dot} rounded-full mr-1`}></div>
              {flag.charAt(0).toUpperCase() + flag.slice(1)}
            </span>
          );
        })}
      </div>
    );
  };

  // Render sentiment with beautiful styling
  const renderSentiment = (sentiment: string) => {
    const sentimentStyles = {
      positive: { bg: 'bg-emerald-100', text: 'text-emerald-800', dot: 'bg-emerald-500' },
      neutral: { bg: 'bg-slate-100', text: 'text-slate-800', dot: 'bg-slate-500' },
      negative: { bg: 'bg-rose-100', text: 'text-rose-800', dot: 'bg-rose-500' }
    };

    const style = sentimentStyles[sentiment.toLowerCase() as keyof typeof sentimentStyles] ||
                  sentimentStyles.neutral;

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${style.bg} ${style.text} shadow-sm`}>
        <div className={`w-2 h-2 ${style.dot} rounded-full mr-2`}></div>
        {sentiment.charAt(0).toUpperCase() + sentiment.slice(1)}
      </span>
    );
  };

  // Format date and time for display
  const formatDateTime = (dateTimeStr: string) => {
    try {
      const date = new Date(dateTimeStr);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return dateTimeStr; // Return the original string if parsing fails
      }

      // Format the date and time
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateTimeStr;
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Bar and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        {/* Search Bar */}
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by counselor name..."
              value={searchQuery}
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onSearchChange?.('')}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-3">
          {/* Sentiment Filter */}
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={sentimentFilter} onValueChange={onSentimentFilterChange}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Sentiment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All</SelectItem>
                <SelectItem value="positive">Positive</SelectItem>
                <SelectItem value="negative">Negative</SelectItem>
                <SelectItem value="neutral">Neutral</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Flags Filter */}
          <div className="flex items-center gap-2">
            <Select value={flagsFilter} onValueChange={onFlagsFilterChange}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Flags" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="follow_up">Follow Up</SelectItem>
                <SelectItem value="escalation">Escalation</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Call Count */}
          <div className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
            <span className="font-medium">{calls.length}</span> {calls.length === 1 ? 'call' : 'calls'}
          </div>
        </div>
      </div>

      <div className="rounded-xl border border-gray-200 overflow-hidden shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-200">
              <TableHead className="w-[120px] font-semibold text-gray-700">Call ID</TableHead>
              <TableHead className="font-semibold text-gray-700">Date & Time</TableHead>
              <TableHead className="font-semibold text-gray-700">Counselor</TableHead>
              <TableHead className="font-semibold text-gray-700">Duration</TableHead>
              <TableHead className="font-semibold text-gray-700">Sentiment</TableHead>
              <TableHead className="font-semibold text-gray-700">Flags</TableHead>
              <TableHead className="text-center font-semibold text-gray-700">Accuracy</TableHead>
              <TableHead className="text-right font-semibold text-gray-700">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentCalls.length > 0 ? (
              currentCalls.map((call, index) => (
                <TableRow
                  key={call.id}
                  className={`hover:bg-blue-50/50 transition-colors duration-200 ${
                    index % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'
                  }`}
                >
                  <TableCell className="font-mono text-sm font-medium text-gray-800">
                    {call.id ? call.id.slice(0, 8) + '...' : 'N/A'}
                  </TableCell>
                  <TableCell className="text-gray-700">
                    {call.dateTime ? formatDateTime(call.dateTime) : 'N/A'}
                  </TableCell>
                  <TableCell className="font-medium text-gray-800">{call.counselor || 'N/A'}</TableCell>
                  <TableCell className="text-gray-700">
                    <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded-md text-sm font-medium">
                      {call.duration || 'N/A'}
                    </span>
                  </TableCell>
                  <TableCell>
                    {call.sentiment ? renderSentiment(call.sentiment) : (
                      <span className="text-gray-400 text-sm">N/A</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {call.flags && call.flags.length > 0 ? renderFlags(call.flags) : (
                      <span className="text-gray-400 text-sm">None</span>
                    )}
                  </TableCell>
                  <TableCell className="text-center">
                    {call.infoAccuracy !== undefined && call.infoAccuracy !== null && typeof call.infoAccuracy === 'number' ? (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        call.infoAccuracy >= 80 ? 'bg-green-100 text-green-800' :
                        call.infoAccuracy >= 60 ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {Math.round(call.infoAccuracy)}%
                      </span>
                    ) : (
                      <span className="text-gray-400 text-sm">N/A</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => viewCallDetails(call.id)}
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-colors duration-200"
                      title="View call details"
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="h-32 text-center">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                      <div className="w-8 h-8 bg-gray-400 rounded-full"></div>
                    </div>
                    <p className="text-lg font-medium">No calls found</p>
                    <p className="text-sm">Try adjusting your filters or date range</p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Beautiful Pagination */}
      {totalItems > 0 && (
        <div className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm">
          <div className="flex flex-col lg:flex-row items-center justify-between space-y-3 lg:space-y-0">
            <div className="flex flex-col sm:flex-row items-center gap-3">
              <div className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
                Showing <span className="font-semibold text-gray-800">{indexOfFirstItem + 1}-{Math.min(indexOfLastItem, totalItems)}</span> of <span className="font-semibold text-gray-800">{totalItems}</span> calls
              </div>
              {onItemsPerPageChange && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Rows per page:</span>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => {
                      const newItemsPerPage = parseInt(value);
                      onItemsPerPageChange(newItemsPerPage);
                      // Reset to page 1 when changing items per page
                      setCurrentPage(1);
                      if (isApiPagination && onPageChange) {
                        onPageChange(0, newItemsPerPage);
                      }
                    }}
                  >
                    <SelectTrigger className="w-20 h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="15">15</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Use pagination.previousPage if available, otherwise fallback to currentPage - 1
                  const prevPage = pagination?.previousPage !== null && pagination?.previousPage !== undefined
                    ? pagination.previousPage
                    : currentPage - 1;
                  console.log(`Previous button clicked, going to page ${prevPage}`);
                  handlePageChange(prevPage);
                }}
                disabled={pagination?.hasPrevious === false || currentPage <= 1}
                title="Go to previous page"
                className="hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200"
              >
                <span className="mr-1">←</span>
                Previous
              </Button>
              <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg">
                <span className="text-sm font-medium text-blue-800">
                  Page {pagination?.currentPage || currentPage}
                </span>
                <span className="text-sm text-blue-600">
                  of {totalPages}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Use pagination.nextPage if available, otherwise fallback to currentPage + 1
                  const nextPage = pagination?.nextPage !== null && pagination?.nextPage !== undefined
                    ? pagination.nextPage
                    : currentPage + 1;
                  console.log(`Next button clicked, going to page ${nextPage}`);
                  handlePageChange(nextPage);
                }}
                disabled={pagination?.hasNext === false || currentPage >= totalPages || totalPages === 0}
                title="Go to next page"
                className="hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200"
              >
                Next
                <span className="ml-1">→</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CallsTable;
