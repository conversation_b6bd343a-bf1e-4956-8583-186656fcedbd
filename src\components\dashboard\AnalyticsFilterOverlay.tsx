import React, { useState, useEffect } from 'react';
import { X, Filter, Bar<PERSON>hart3, Check, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import DateFilterPanel from './DateFilterPanel';
import { DateFilters } from '@/services/dashboardService';
import { cn } from '@/lib/utils';

interface AnalyticsFilterOverlayProps {
  dateFilters: DateFilters;
  onApplyFilters: (dateFilters: DateFilters) => void;
  onClearAllFilters: () => void;
  isLoading?: boolean;
  className?: string;
}

const AnalyticsFilterOverlay: React.FC<AnalyticsFilterOverlayProps> = ({
  dateFilters,
  onApplyFilters,
  onClearAllFilters,
  isLoading = false,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  // Local state for pending filter changes
  const [localDateFilters, setLocalDateFilters] = useState<DateFilters>(dateFilters);
  
  // Track if there are pending changes
  const [hasUnappliedChanges, setHasUnappliedChanges] = useState(false);

  // Update local state when props change (e.g., when filters are applied or cleared externally)
  useEffect(() => {
    setLocalDateFilters(dateFilters);
    setHasUnappliedChanges(false);
  }, [dateFilters]);

  // Check for changes whenever local filters change
  useEffect(() => {
    const hasChanges = JSON.stringify(localDateFilters) !== JSON.stringify(dateFilters);
    setHasUnappliedChanges(hasChanges);
  }, [localDateFilters, dateFilters]);

  const handleApplyFilters = () => {
    console.log('Applying analytics filters:', localDateFilters);
    onApplyFilters(localDateFilters);
    setHasUnappliedChanges(false);
    setIsOpen(false);
  };

  const handleClearDateFilters = () => {
    console.log('Clearing date filters');
    setLocalDateFilters({ filter_type: 'all' });
  };

  const handleClearAllFilters = () => {
    console.log('Clearing all analytics filters');
    setLocalDateFilters({ filter_type: 'all' });
    onClearAllFilters();
    setHasUnappliedChanges(false);
    setIsOpen(false);
  };

  const getTotalFilters = () => {
    let count = 0;
    if (dateFilters.filter_type !== 'all') count++;
    return count;
  };

  const totalFilters = getTotalFilters();

  return (
    <div className={className}>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="relative bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100 hover:border-blue-300"
          >
            <Filter className="h-4 w-4 mr-2" />
            Analytics
            {totalFilters > 0 && (
              <Badge 
                variant="secondary" 
                className="ml-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600 text-white"
              >
                {totalFilters}
              </Badge>
            )}
          </Button>
        </DialogTrigger>
        
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-blue-800">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Analytics Filters
              {totalFilters > 0 && (
                <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
                  {totalFilters} active
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Date Filters */}
            <DateFilterPanel
              dateFilters={localDateFilters}
              onDateFiltersChange={setLocalDateFilters}
              onClearFilters={handleClearDateFilters}
              isLoading={isLoading}
              showAnalyticsLabel={false}
            />

            {/* Filter Impact Notice */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="text-xs text-blue-700 flex flex-col sm:flex-row sm:items-center gap-2">
                <span className="font-medium text-blue-800">📊 Analytics Impact:</span>
                <span>These filters affect dashboard metrics, charts, and analytics data</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAllFilters}
                className="text-gray-600 hover:text-gray-800"
                disabled={isLoading || totalFilters === 0}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Clear All
              </Button>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleApplyFilters}
                  disabled={isLoading || !hasUnappliedChanges}
                  className={cn(
                    "bg-blue-600 hover:bg-blue-700 text-white",
                    hasUnappliedChanges && "animate-pulse"
                  )}
                >
                  {isLoading ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Check className="h-4 w-4 mr-2" />
                  )}
                  Apply Filters
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AnalyticsFilterOverlay;
